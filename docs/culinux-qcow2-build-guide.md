# CULinux 4.0 qcow2 镜像构建文档

## 概述

本文档介绍如何使用 virt-install 手动安装方式构建 CULinux 4.0 qcow2 镜像文件。

## 核心步骤

### 1. 环境准备

确认宿主机环境：
```bash
# 检查 KVM 支持
lscpu | grep -i virtualization
ls -la /dev/kvm

# 检查 QEMU 版本
/usr/libexec/qemu-kvm --version

```

### 2. 创建虚拟磁盘

```bash
# 创建 qcow2 磁盘文件
qemu-img create -f qcow2 culinux-4.0-base.qcow2 50G
```

### 3. 启动虚拟机安装

```bash
  virt-install \
  --virt-type kvm \
  --name culinux-4.0-build \
  --ram 4096 \
  --vcpus 4 \
  --disk culinux-4.0-base-ll.qcow2,format=qcow2,bus=virtio \
  --network network=default,model=virtio \
  --graphics vnc,listen=0.0.0.0 \
  --video vga \
  --noautoconsole \
  --os-type=linux \
  --os-variant=generic \
  --cdrom=/opt/packer-base-image/os/CULinux-4.0-kr720.x86_64.iso
```

**virt-install会增加如下参数进行适配**
- `--machine pc-i440fx-6.2`：使用兼容的机器类型，避免kernel panic
- `--disk bus=ide`：使用IDE总线，兼容性更好
- `--network model=e1000`：使用e1000网卡模型，驱动支持更好

### 4. 连接安装界面

虚拟机启动后，通过 VNC 连接到图形安装界面：

```bash
# 查看虚拟机状态
virsh list --all

# 查看 VNC 端口分配
virsh vncdisplay culinux-4.0-build

# 连接 VNC（假设显示 :0，对应端口 5900）
vncviewer <服务器IP>:5900
```

### 5. 手动安装配置

#### 启动安装程序

通过 VNC 连接到虚拟机后，会看到 CULinux 4.0 安装界面。选择 "Install CULinux 4.0" 进入安装。

#### 安装配置界面

安装程序启动后会进入图形化配置界面，主要包含以下配置项：

**1. 网络和主机名**
- **网络配置**：系统会自动检测并配置网络接口
- 虚拟机创建时已自动分配网卡，通常显示为 "已连接" 状态

**2. 软件选择**
- 选择 **最小安装** 或 **服务器安装**
- 建议选择最小安装以减小镜像大小
- 可以勾选 "开发工具" 如果需要编译环境

**3. 安装目的地（磁盘分区）**
- 点击进入磁盘分区配置
- 选择目标磁盘（显示为创建的 qcow2 文件大小）
- 推荐使用 **自动分区**，系统会自动创建合理的分区方案：
  ```
  /boot/efi  200MB   FAT32 (如果是UEFI)
  /boot      1GB     XFS
  swap       2GB     swap
  /          剩余空间  XFS (LVM)
  ```
- 高级用户可选择手动分区进行自定义

**4. 时间和日期**
- 设置时区为 **Asia/Shanghai**
- 可以启用网络时间同步

**5. 键盘布局**
- 保持默认 US 布局或根据需要调整

**6. 语言支持**
- 可以添加中文支持，但建议最小安装时保持英文

**7. 用户设置**
- **Root 密码**：设置为 `Culinux4@2025`（或根据安全策略设置）
- **创建用户**：可选择创建普通用户或仅使用 root
- 确保启用 root 账户SSH登录

#### 开始安装

确认所有配置项都已正确设置后（界面上不应有警告标志），点击 **"开始安装"** 按钮。

安装过程大约需要 10-20 分钟，取决于选择的软件包数量和硬件性能。

### 6. 安装后配置

安装完成重启后，登录系统进行基本配置：

```bash
# 更新系统
yum update -y

# 安装云计算相关包
yum install -y cloud-init cloud-utils-growpart acpid

# 启用必要服务
systemctl enable sshd
systemctl enable acpid
systemctl enable cloud-init
systemctl enable cloud-config
systemctl enable cloud-final
systemctl enable cloud-init-local

# 配置防火墙（推荐禁用用于镜像制作）
systemctl stop firewalld
systemctl disable firewalld

# 配置SELinux（推荐禁用）
sed -i 's/^SELINUX=enforcing/SELINUX=disabled/' /etc/selinux/config
sed -i 's/^SELINUX=permissive/SELINUX=disabled/' /etc/selinux/config
```

### 7. 串口控制台配置

为支持云平台的控制台访问，需要配置串口控制台：

```bash
# 1. 配置安全终端
grep -q ttyS0 /etc/securetty || echo "ttyS0" >> /etc/securetty

# 2. 配置GRUB添加串口控制台参数
vi /etc/default/grub
# 在GRUB_CMDLINE_LINUX行末尾添加: console=tty0 console=ttyS0,115200

# 3. 重新生成GRUB配置
grub2-mkconfig -o /boot/grub2/grub.cfg

# 4. 启用串口登录服务
<NAME_EMAIL>
<NAME_EMAIL>
```

### 8. 验证镜像

```bash
# 查看镜像信息
qemu-img info culinux-4.0-base.qcow2

# 测试启动
virt-install \
--name culinux-4.0-base \
--memory 4096 \
--vcpus 4 \
--disk path=culinux-4.0-x86_64_base.qcow2,format=qcow2 \
--import \
--os-variant generic \
--network default \
--video virtio \
--graphics vnc,listen=0.0.0.0 \
--noautoconsole

```