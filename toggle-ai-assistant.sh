#!/bin/bash
# VSCode AI助手快速开关脚本
# 解决中文输入法冲突问题

set -uo pipefail

SETTINGS_FILE=".vscode/settings.json"
BACKUP_FILE=".vscode/settings.json.backup"

# 日志函数
log() {
    echo "[$(date '+%H:%M:%S')] $1"
}

# 检查是否在项目根目录
check_project_root() {
    if [[ ! -d ".vscode" ]]; then
        log "错误: 请在项目根目录运行此脚本"
        exit 1
    fi
}

# 禁用AI助手（中文输入模式）
disable_ai() {
    log "🚫 禁用所有AI助手（中文输入模式）"
    
    # 备份当前设置
    if [[ -f "$SETTINGS_FILE" ]]; then
        cp "$SETTINGS_FILE" "$BACKUP_FILE"
        log "✅ 已备份当前设置"
    fi
    
    # 创建禁用AI的配置
    cat > "$SETTINGS_FILE" << 'EOF'
{
  // 🇨🇳 中文输入优化模式 - 完全禁用AI助手
  "editor.inlineSuggest.enabled": false,
  "editor.quickSuggestions": false,
  "editor.parameterHints.enabled": false,
  "editor.suggestOnTriggerCharacters": false,
  "editor.acceptSuggestionOnCommitCharacter": false,
  "editor.acceptSuggestionOnEnter": "off",
  "editor.wordBasedSuggestions": "off",
  "editor.suggest.enabled": false,
  "editor.tabCompletion": "off",
  "editor.snippetSuggestions": "none",
  
  // 禁用所有AI助手
  "augment.enable": {
    "*": false
  },
  "github.copilot.enable": {
    "*": false
  },
  "github.copilot.editor.enableAutoCompletions": false,
  "roo-cline.enable": false,
  
  // 性能优化
  "editor.semanticHighlighting.enabled": false,
  "editor.bracketPairColorization.enabled": false,
  "editor.occurrencesHighlight": "off",
  "editor.selectionHighlight": false,
  "editor.minimap.enabled": false,
  "editor.hover.delay": 5000,
  "editor.quickSuggestionsDelay": 5000
}
EOF
    
    log "✅ AI助手已禁用，中文输入应该更流畅了"
    log "💡 使用 './toggle-ai-assistant.sh enable' 重新启用"
}

# 启用AI助手（编程模式）
enable_ai() {
    log "🤖 启用AI助手（编程模式）"
    
    if [[ -f "$BACKUP_FILE" ]]; then
        cp "$BACKUP_FILE" "$SETTINGS_FILE"
        log "✅ 已恢复之前的设置"
    else
        # 创建启用AI的基本配置
        cat > "$SETTINGS_FILE" << 'EOF'
{
  // 🤖 编程模式 - 启用AI助手
  "editor.inlineSuggest.enabled": true,
  "editor.quickSuggestions": {
    "other": true,
    "comments": true,
    "strings": true
  },
  "editor.parameterHints.enabled": true,
  "editor.suggestOnTriggerCharacters": true,
  "editor.acceptSuggestionOnCommitCharacter": true,
  "editor.acceptSuggestionOnEnter": "on",
  "editor.wordBasedSuggestions": "matchingDocuments",
  "editor.suggest.enabled": true,
  "editor.tabCompletion": "on",
  "editor.snippetSuggestions": "top",
  
  // 启用AI助手
  "augment.enable": {
    "*": true
  },
  "github.copilot.enable": {
    "*": true
  },
  "github.copilot.editor.enableAutoCompletions": true
}
EOF
        log "✅ 已创建启用AI的配置"
    fi
    
    log "💡 使用 './toggle-ai-assistant.sh disable' 禁用AI助手"
}

# 显示当前状态
show_status() {
    log "📊 当前AI助手状态："
    
    if [[ -f "$SETTINGS_FILE" ]]; then
        if grep -q '"editor.inlineSuggest.enabled": false' "$SETTINGS_FILE"; then
            echo "   🚫 AI助手已禁用（中文输入模式）"
        else
            echo "   🤖 AI助手已启用（编程模式）"
        fi
        
        if grep -q '"augment.enable"' "$SETTINGS_FILE"; then
            echo "   📝 检测到Augment配置"
        fi
        
        if grep -q '"github.copilot.enable"' "$SETTINGS_FILE"; then
            echo "   🐙 检测到GitHub Copilot配置"
        fi
    else
        echo "   ❓ 未找到VSCode配置文件"
    fi
}

# 显示帮助信息
show_help() {
    cat << 'EOF'
🛠️  VSCode AI助手快速开关工具

用法:
  ./toggle-ai-assistant.sh <command>

命令:
  disable    禁用AI助手（中文输入模式）
  enable     启用AI助手（编程模式）
  status     显示当前状态
  help       显示此帮助信息

示例:
  # 禁用AI助手，优化中文输入
  ./toggle-ai-assistant.sh disable
  
  # 重新启用AI助手进行编程
  ./toggle-ai-assistant.sh enable
  
  # 查看当前状态
  ./toggle-ai-assistant.sh status

💡 提示: 
  - 编辑中文内容时使用 disable 模式
  - 编写代码时使用 enable 模式
  - 配置会自动备份和恢复
EOF
}

# 主函数
main() {
    check_project_root
    
    case "${1:-help}" in
        "disable")
            disable_ai
            ;;
        "enable")
            enable_ai
            ;;
        "status")
            show_status
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log "❌ 未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

main "$@"
