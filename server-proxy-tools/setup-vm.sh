#!/bin/bash
#
# 虚机代理配置脚本
# 自动配置虚机的代理环境变量
#
# 使用方法：
#     ./setup-vm.sh [SERVER_IP]
#
# 参数：
#     SERVER_IP: 服务器IP地址，默认使用*************
#

set -uo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取服务器IP地址
get_server_ip() {
    if [[ $# -gt 0 ]]; then
        echo "$1"
    else
        # 默认使用虚机网关IP
        echo "*************"
    fi
}

# 测试代理连接
test_proxy() {
    local proxy_url="$1"
    log "测试代理连接: $proxy_url"
    
    if curl -I --connect-timeout 10 --proxy "$proxy_url" https://www.google.com >/dev/null 2>&1; then
        log "✅ 代理连接测试成功"
        return 0
    else
        error "❌ 代理连接测试失败"
        return 1
    fi
}

# 配置代理环境变量
setup_proxy() {
    local server_ip="$1"
    local proxy_url="http://${server_ip}:3128"
    
    log "配置代理环境变量..."
    
    # 设置当前会话的代理
    export http_proxy="$proxy_url"
    export https_proxy="$proxy_url"
    export HTTP_PROXY="$proxy_url"
    export HTTPS_PROXY="$proxy_url"
    
    log "当前会话代理已配置: $proxy_url"

    # 提示用户如何在当前shell中应用环境变量
    echo
    echo -e "${YELLOW}注意：要在当前shell中应用代理设置，请执行以下命令：${NC}"
    echo -e "${BLUE}eval \"\$(./setup-vm.sh --export-env)\"${NC}"
    echo
    
    # 询问是否永久配置
    echo
    read -p "是否永久配置代理到 ~/.bashrc? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "配置永久代理..."
        
        # 备份原始bashrc
        if [[ -f ~/.bashrc ]]; then
            cp ~/.bashrc ~/.bashrc.backup.$(date +%Y%m%d_%H%M%S)
            log "已备份 ~/.bashrc"
        fi
        
        # 添加代理配置
        cat >> ~/.bashrc << EOF

# 代理配置 - 由 setup-vm.sh 添加
export http_proxy="$proxy_url"
export https_proxy="$proxy_url"
export HTTP_PROXY="$proxy_url"
export HTTPS_PROXY="$proxy_url"
EOF
        
        log "✅ 永久代理配置已添加到 ~/.bashrc"
        log "下次登录时自动生效，或执行: source ~/.bashrc"
    fi
}

# 配置Git代理
setup_git_proxy() {
    local server_ip="$1"
    local proxy_url="http://${server_ip}:3128"
    
    echo
    read -p "是否配置Git代理? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log "配置Git代理..."
        
        git config --global http.proxy "$proxy_url"
        git config --global https.proxy "$proxy_url"
        
        log "✅ Git代理配置完成"
        log "查看配置: git config --global --list | grep proxy"
    fi
}

# 测试网络访问
test_network() {
    log "测试网络访问..."
    
    echo
    log "测试 Google 访问:"
    if curl -I --connect-timeout 10 https://www.google.com 2>/dev/null | head -1; then
        log "✅ Google 访问成功"
    else
        error "❌ Google 访问失败"
    fi
    
    echo
    log "测试 GitHub 访问:"
    if curl -I --connect-timeout 10 https://github.com 2>/dev/null | head -1; then
        log "✅ GitHub 访问成功"
    else
        error "❌ GitHub 访问失败"
    fi
    
    # 测试Git clone（如果配置了Git代理）
    if git config --global http.proxy >/dev/null 2>&1; then
        echo
        log "测试 Git clone:"
        if timeout 10 git ls-remote https://github.com/octocat/Hello-World.git >/dev/null 2>&1; then
            log "✅ Git 访问成功"
        else
            warn "⚠️ Git 访问可能有问题"
        fi
    fi
}

# 显示使用说明
show_usage() {
    echo
    log "虚机代理配置完成！"
    echo
    echo -e "${YELLOW}⚠️  重要提示：要在当前shell中使用代理，请执行：${NC}"
    echo -e "${GREEN}eval \"\$(./setup-vm.sh --export-env)\"${NC}"
    echo
    echo -e "${BLUE}常用测试命令:${NC}"
    echo "  curl -I https://www.google.com"
    echo "  curl -I https://github.com"
    echo "  git clone https://github.com/用户名/仓库名.git"
    echo "  pip install 包名"
    echo "  yum install 包名"
    echo
    echo -e "${BLUE}取消代理:${NC}"
    echo "  unset http_proxy https_proxy HTTP_PROXY HTTPS_PROXY"
    echo "  git config --global --unset http.proxy"
    echo "  git config --global --unset https.proxy"
    echo
}

# 输出环境变量设置命令
export_env() {
    local server_ip="${1:-*************}"
    local proxy_url="http://${server_ip}:3128"

    # 输出可以被eval的环境变量设置命令
    cat << EOF
export http_proxy="$proxy_url"
export https_proxy="$proxy_url"
export HTTP_PROXY="$proxy_url"
export HTTPS_PROXY="$proxy_url"
echo "虚机代理环境变量已设置: $proxy_url"
EOF
}

# 主函数
main() {
    # 检查是否是导出环境变量模式
    if [[ "${1:-}" == "--export-env" ]]; then
        shift
        export_env "$@"
        return $?
    fi

    echo "============================================================"
    log "虚机代理配置脚本"
    echo "============================================================"
    
    # 获取服务器IP
    server_ip=$(get_server_ip "$@")
    log "使用服务器IP: $server_ip"
    
    # 测试代理连接
    if ! test_proxy "http://${server_ip}:3128"; then
        error "请确保服务器上的虚机代理服务器正在运行:"
        error "  python3 server-vm-proxy.py"
        exit 1
    fi
    
    # 配置代理
    setup_proxy "$server_ip"
    
    # 配置Git代理
    setup_git_proxy "$server_ip"
    
    # 测试网络访问
    test_network
    
    # 显示使用说明
    show_usage
    
    log "🎉 配置完成！"
}

# 执行主函数
main "$@"
