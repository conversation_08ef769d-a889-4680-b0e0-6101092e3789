#!/bin/bash
# VM中的OpenStack回归测试环境配置脚本

set -e

K8S_NODE_IP="your-k8s-node-ip"
OPENSTACK_USER="regression-test-user"
OPENSTACK_PASSWORD="regression-test-password"
OPENSTACK_PROJECT="regression-test-project"

echo "🚀 配置VM中的OpenStack回归测试环境..."

# 1. 安装OpenStack客户端
echo "📦 安装OpenStack客户端..."
sudo apt-get update
sudo apt-get install -y python3-pip python3-venv curl

# 创建虚拟环境
python3 -m venv /opt/openstack-venv
source /opt/openstack-venv/bin/activate

# 安装OpenStack客户端
pip install python-openstackclient python-troveclient python-neutronclient

# 2. 配置OpenStack认证
echo "🔑 配置OpenStack认证..."
mkdir -p ~/.config/openstack

cat > ~/.config/openstack/clouds.yaml <<EOF
clouds:
  regression-test:
    auth:
      auth_url: http://$K8S_NODE_IP:30500/v3
      username: $OPENSTACK_USER
      password: $OPENSTACK_PASSWORD
      project_name: $OPENSTACK_PROJECT
      domain_name: default
    region_name: RegionOne
    interface: public
    identity_api_version: 3
    volume_api_version: 3
    compute_api_version: 2.1
    database_api_version: 1.0
    network_api_version: 2.0
EOF

# 3. 创建环境变量文件
cat > ~/.openstack_rc <<EOF
export OS_CLOUD=regression-test
export OS_AUTH_URL=http://$K8S_NODE_IP:30500/v3
export OS_USERNAME=$OPENSTACK_USER
export OS_PASSWORD=$OPENSTACK_PASSWORD
export OS_PROJECT_NAME=$OPENSTACK_PROJECT
export OS_DOMAIN_NAME=default
export OS_IDENTITY_API_VERSION=3
EOF

# 4. 创建测试脚本目录
mkdir -p /opt/regression-tests

# 5. 创建Trove测试脚本
cat > /opt/regression-tests/test-trove.sh <<'EOF'
#!/bin/bash
set -e

echo "🔍 测试Trove数据库服务..."

# 激活虚拟环境
source /opt/openstack-venv/bin/activate
source ~/.openstack_rc

# 测试连通性
echo "Testing OpenStack connectivity..."
openstack token issue

# 测试数据库实例操作
echo "Testing database instance operations..."

# MySQL测试
MYSQL_INSTANCE="test-mysql-$(date +%s)"
echo "Creating MySQL instance: $MYSQL_INSTANCE"
openstack database instance create $MYSQL_INSTANCE mysql-5.7 2 --size 1

# 等待实例创建完成
echo "Waiting for instance to become ACTIVE..."
timeout 600 bash -c "
while [[ \"\$(openstack database instance show $MYSQL_INSTANCE -f value -c status)\" != \"ACTIVE\" ]]; do
    echo \"Instance status: \$(openstack database instance show $MYSQL_INSTANCE -f value -c status)\"
    sleep 30
done"

# 验证实例
openstack database instance show $MYSQL_INSTANCE

# 测试实例重启
echo "Testing instance restart..."
openstack database instance restart $MYSQL_INSTANCE

# 等待重启完成
sleep 60

# 清理资源
echo "Cleaning up resources..."
openstack database instance delete $MYSQL_INSTANCE

echo "✅ Trove MySQL测试完成"

# Redis测试
REDIS_INSTANCE="test-redis-$(date +%s)"
echo "Creating Redis instance: $REDIS_INSTANCE"
openstack database instance create $REDIS_INSTANCE redis-6.0 1 --size 1

# 等待实例创建完成
timeout 600 bash -c "
while [[ \"\$(openstack database instance show $REDIS_INSTANCE -f value -c status)\" != \"ACTIVE\" ]]; do
    echo \"Instance status: \$(openstack database instance show $REDIS_INSTANCE -f value -c status)\"
    sleep 30
done"

# 清理资源
openstack database instance delete $REDIS_INSTANCE

echo "✅ Trove Redis测试完成"
EOF

# 6. 创建Neutron测试脚本
cat > /opt/regression-tests/test-neutron.sh <<'EOF'
#!/bin/bash
set -e

echo "🔍 测试Neutron网络服务..."

# 激活虚拟环境
source /opt/openstack-venv/bin/activate
source ~/.openstack_rc

NETWORK_NAME="test-network-$(date +%s)"
SUBNET_NAME="test-subnet-$(date +%s)"
ROUTER_NAME="test-router-$(date +%s)"

# 创建网络
echo "Creating network: $NETWORK_NAME"
openstack network create $NETWORK_NAME

# 创建子网
echo "Creating subnet: $SUBNET_NAME"
openstack subnet create $SUBNET_NAME \
    --network $NETWORK_NAME \
    --subnet-range 192.168.100.0/24 \
    --dns-nameserver 8.8.8.8

# 创建路由器
echo "Creating router: $ROUTER_NAME"
openstack router create $ROUTER_NAME

# 添加子网到路由器
openstack router add subnet $ROUTER_NAME $SUBNET_NAME

# 验证网络配置
echo "Verifying network configuration..."
openstack network show $NETWORK_NAME
openstack subnet show $SUBNET_NAME
openstack router show $ROUTER_NAME

# 清理资源
echo "Cleaning up resources..."
openstack router remove subnet $ROUTER_NAME $SUBNET_NAME
openstack router delete $ROUTER_NAME
openstack subnet delete $SUBNET_NAME
openstack network delete $NETWORK_NAME

echo "✅ Neutron测试完成"
EOF

# 7. 创建主测试脚本
cat > /opt/regression-tests/run-all-tests.sh <<'EOF'
#!/bin/bash
set -e

echo "🚀 开始执行OpenStack回归测试..."

# 记录开始时间
START_TIME=$(date)
echo "测试开始时间: $START_TIME"

# 创建测试结果目录
RESULT_DIR="/tmp/regression-results-$(date +%Y%m%d-%H%M%S)"
mkdir -p $RESULT_DIR

# 执行Trove测试
echo "执行Trove测试..."
if bash /opt/regression-tests/test-trove.sh > $RESULT_DIR/trove-test.log 2>&1; then
    echo "✅ Trove测试通过"
    echo "PASS" > $RESULT_DIR/trove-result.txt
else
    echo "❌ Trove测试失败"
    echo "FAIL" > $RESULT_DIR/trove-result.txt
fi

# 执行Neutron测试
echo "执行Neutron测试..."
if bash /opt/regression-tests/test-neutron.sh > $RESULT_DIR/neutron-test.log 2>&1; then
    echo "✅ Neutron测试通过"
    echo "PASS" > $RESULT_DIR/neutron-result.txt
else
    echo "❌ Neutron测试失败"
    echo "FAIL" > $RESULT_DIR/neutron-result.txt
fi

# 生成测试报告
echo "生成测试报告..."
cat > $RESULT_DIR/test-report.txt <<EOL
OpenStack回归测试报告
==================

测试时间: $START_TIME - $(date)
测试环境: VM回归测试环境

测试结果:
- Trove测试: $(cat $RESULT_DIR/trove-result.txt)
- Neutron测试: $(cat $RESULT_DIR/neutron-result.txt)

详细日志请查看:
- Trove: $RESULT_DIR/trove-test.log
- Neutron: $RESULT_DIR/neutron-test.log
EOL

echo "📊 测试报告已生成: $RESULT_DIR/test-report.txt"
cat $RESULT_DIR/test-report.txt

echo "🎉 回归测试完成！"
EOF

# 8. 设置脚本权限
chmod +x /opt/regression-tests/*.sh

# 9. 创建systemd服务（可选）
cat > /etc/systemd/system/openstack-regression.service <<EOF
[Unit]
Description=OpenStack Regression Test Service
After=network.target

[Service]
Type=oneshot
ExecStart=/opt/regression-tests/run-all-tests.sh
User=root
WorkingDirectory=/opt/regression-tests

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload

echo "✅ VM回归测试环境配置完成！"
echo ""
echo "📋 使用方法："
echo "1. 手动执行所有测试："
echo "   /opt/regression-tests/run-all-tests.sh"
echo ""
echo "2. 单独执行Trove测试："
echo "   /opt/regression-tests/test-trove.sh
ech
echo "3. 单独执行Neutron测试："
echo "   /opt/regression-tests/test-neutron.sh"
echo ""
echo "4. 通过systemd执行："
echo "   systemctl start openstack-regression"
echo ""
echo "⚠️  注意事项："
echo "- 确保K8s节点IP ($K8S_NODE_IP) 可访问"
echo "- 确保OpenStack服务已通过NodePort暴露"
echo "- 确保测试用户和项目已在OpenStack中创建"

echo "-确保用户输入